import React, { useMemo } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useDocuments } from '@/contexts/DocumentContext';
import { Document } from '@/models/document';
import DocumentRow from './DocumentRow';
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  FileText,
  Inbox
} from 'lucide-react';

const DocumentTable: React.FC = () => {
  const {
    documents,
    searchFilters,
    sortConfig,
    setSortConfig
  } = useDocuments();

  // Filter and sort documents
  const filteredAndSortedDocuments = useMemo(() => {
    let filtered = documents.filter(doc => {
      const matchesSearch = !searchFilters.searchTerm ||
        doc.documentTitle.toLowerCase().includes(searchFilters.searchTerm.toLowerCase()) ||
        doc.documentReferenceNumber.toLowerCase().includes(searchFilters.searchTerm.toLowerCase());

      const matchesStatus = !searchFilters.statusFilter || doc.status === searchFilters.statusFilter;

      const matchesAccessLevel = doc.accessLevel === 'Public';
      return matchesSearch && matchesStatus && matchesAccessLevel;
    });

    // Apply sorting
    if (sortConfig.column) {
      filtered.sort((a, b) => {
        const aVal = a[sortConfig.column!];
        const bVal = b[sortConfig.column!];

        // Handle date sorting
        if (sortConfig.column === 'issueDate') {
          const aDate = new Date(aVal as string);
          const bDate = new Date(bVal as string);
          return sortConfig.direction === 'asc'
            ? aDate.getTime() - bDate.getTime()
            : bDate.getTime() - aDate.getTime();
        }

        // Handle string sorting
        if (typeof aVal === 'string' && typeof bVal === 'string') {
          const aStr = aVal.toLowerCase();
          const bStr = bVal.toLowerCase();

          if (aStr < bStr) return sortConfig.direction === 'asc' ? -1 : 1;
          if (aStr > bStr) return sortConfig.direction === 'asc' ? 1 : -1;
          return 0;
        }

        return 0;
      });
    }

    return filtered;
  }, [documents, searchFilters, sortConfig]);

  const handleSort = (column: keyof Document) => {
    const direction =
      sortConfig.column === column && sortConfig.direction === 'asc'
        ? 'desc'
        : 'asc';

    setSortConfig({ column, direction });
  };

  const getSortIcon = (column: keyof Document) => {
    if (sortConfig.column !== column) {
      return <ArrowUpDown className="h-4 w-4" />;
    }
    return sortConfig.direction === 'asc'
      ? <ArrowUp className="h-4 w-4" />
      : <ArrowDown className="h-4 w-4" />;
  };

  const sortableColumns = [
    { key: 'documentReferenceNumber' as keyof Document, label: 'Reference Number' },
    { key: 'documentTitle' as keyof Document, label: 'Title' },
    { key: 'version' as keyof Document, label: 'Version' },
    { key: 'issueDate' as keyof Document, label: 'Issue Date' },
    { key: 'authorFunction' as keyof Document, label: 'Author' },
    { key: 'responsibleDepartment' as keyof Document, label: 'Department' },
    { key: 'status' as keyof Document, label: 'Status' },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Document List
          </div>
          <div className="text-sm font-normal text-muted-foreground">
            {filteredAndSortedDocuments.length === documents.length
              ? `${documents.length} document${documents.length !== 1 ? 's' : ''}`
              : `${filteredAndSortedDocuments.length} of ${documents.length} document${documents.length !== 1 ? 's' : ''}`
            }
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {filteredAndSortedDocuments.length === 0 ? (
          <div className="text-center py-12">
            <Inbox className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No documents found</h3>
            <p className="text-muted-foreground">
              {documents.length === 0
                ? 'Start by adding your first document.'
                : 'No documents match your search criteria. Try adjusting your filters.'
              }
            </p>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  {sortableColumns.map((column) => (
                    <TableHead key={column.key}>
                      <Button
                        variant="ghost"
                        onClick={() => handleSort(column.key)}
                        className="h-auto p-0 font-medium hover:bg-transparent"
                      >
                        {column.label}
                        <span className="ml-2">
                          {getSortIcon(column.key)}
                        </span>
                      </Button>
                    </TableHead>
                  ))}
                  <TableHead>Attachment</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAndSortedDocuments.map(document => (
                  <DocumentRow key={document.id} document={document} />
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DocumentTable;
