import React, { useEffect, useState } from 'react';
import { toast } from '@/hooks/use-toast';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useDocuments } from '@/contexts/DocumentContext';
import { DocumentFormData, DOCUMENT_TYPES, DOCUMENT_FORMATS, DOCUMENT_STATUSES, DOCUMENT_ACCESS_LEVELS } from '@/models/document';
import FileUpload from './FileUpload';
import { Plus, Save, X, Edit } from 'lucide-react';
import { Control } from 'react-hook-form';
import { ControllerRenderProps } from 'react-hook-form';
import { UseFormStateReturn } from 'react-hook-form';
import { ControllerFieldState } from 'react-hook-form';

const documentSchema = z.object({
  documentReferenceNumber: z.string().min(1, 'Document Reference Number is required'),
  documentTitle: z.string().min(1, 'Document Title is required'),
  version: z.number().min(0, 'Version must be a positive number'),
  issueDate: z.string().min(1, 'Issue Date is required'),
  authorFunction: z.string().min(1, 'Author Function is required'),
  responsibleDepartment: z.string().min(1, 'Responsible Department is required'),
  status: z.string().min(1, 'Status is required'),
  approvalDate: z.string().optional(),
  approvedByFunction: z.string().optional(),
  revisionDate: z.string().optional(),
  revisionReason: z.string().optional(),
  previousReferenceNumber: z.string().optional(),
  storageLocation: z.string().min(1, 'Storage Location is required'),
  distributionMethod: z.string().optional(),
  remarks: z.string().optional(),
  accessLevel: z.string().min(1, 'Access Level is required'),
});

const DocumentForm: React.FC = () => {
  const { 
    addDocument, 
    updateDocument, 
    currentEditId, 
    setCurrentEditId, 
    documents,
    isLoading 
  } = useDocuments();

  const [currentFile, setCurrentFile] = useState<File | null>(null);

  const form = useForm<DocumentFormData>({
    resolver: zodResolver(documentSchema),
    defaultValues: {
      documentReferenceNumber: '',
      documentTitle: '',
      version: 0,
      issueDate: '',
      authorFunction: '',
      responsibleDepartment: '',
      status: DOCUMENT_STATUSES[0],
      storageLocation: '',
      accessLevel: ''
    }
  });

  // Load document data when editing
  useEffect(() => {
    if (currentEditId) {
      const document = documents.find(doc => doc.id === currentEditId);
      if (document) {
        form.reset({
          documentReferenceNumber: document.documentReferenceNumber,
          documentTitle: document.documentTitle,
          version: document.version,
          issueDate: document.issueDate,
          authorFunction: document.authorFunction,
          responsibleDepartment: document.responsibleDepartment,
          status: document.status,
          approvalDate: document.approvalDate,
          approvedByFunction: document.approvedByFunction,
          revisionDate: document.revisionDate,
          revisionReason: document.revisionReason,
          previousReferenceNumber: document.previousReferenceNumber,
          storageLocation: document.storageLocation,
          distributionMethod: document.distributionMethod,
          remarks: document.remarks,
          accessLevel: document.accessLevel,
        });
      }
    }
  }, [currentEditId, documents, form]);

  const onSubmit = async (data: DocumentFormData) => {
    try {
      const documentData = {
        documentReferenceNumber: data.documentReferenceNumber,
        documentTitle: data.documentTitle,
        version: data.version,
        issueDate: data.issueDate,
        authorFunction: data.authorFunction,
        responsibleDepartment: data.responsibleDepartment,
        status: data.status === '' ? DOCUMENT_STATUSES[0] : data.status,
        approvalDate: data.approvalDate,
        approvedByFunction: data.approvedByFunction,
        revisionDate: data.revisionDate,
        revisionReason: data.revisionReason,
        previousReferenceNumber: data.previousReferenceNumber,
        storageLocation: data.storageLocation,
        distributionMethod: data.distributionMethod,
        remarks: data.remarks,
        accessLevel: data.accessLevel === '' ? DOCUMENT_ACCESS_LEVELS[0] : data.accessLevel,
        fileName: currentFile?.name || '',
        fileData: currentFile ? URL.createObjectURL(currentFile) : '',
      };

      if (currentEditId) {
        updateDocument(currentEditId, documentData);
      } else {
        addDocument(documentData);
      }

      handleClear();
    } catch (error) {
      console.error('Error saving document:', error);
      toast({
        title: "Error",
        description: "Failed to save document. Please check the form data and try again.",
        variant: "destructive",
      });
    }
  };

  const handleClear = () => {
    form.reset();
    setCurrentEditId(null);
    setCurrentFile(null);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {currentEditId ? (
            <>
              <Edit className="h-5 w-5" />
              Edit Document
            </>
          ) : (
            <>
              <Plus className="h-5 w-5" />
              Add New Document
            </>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="documentReferenceNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Document Reference Number *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter document reference number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="documentTitle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Document Title *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter document title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="version"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Version *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter version" type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="issueDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Issue Date *</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="authorFunction"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Author Function *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter author function" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="responsibleDepartment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Responsible Department *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter responsible department" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status *</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value as string}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status..." />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {DOCUMENT_STATUSES.map(status => (
                          <SelectItem key={status} value={status}>{status}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="accessLevel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Access Level *</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value as string}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select access level..." />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {DOCUMENT_ACCESS_LEVELS.map(accessLevel => (
                          <SelectItem key={accessLevel} value={accessLevel}>{accessLevel}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FileUpload
              currentFile={currentFile}
              onFileChange={setCurrentFile}
            />

            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleClear}
                disabled={isLoading}
              >
                <X className="h-4 w-4 mr-2" />
                Clear
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
              >
                <Save className="h-4 w-4 mr-2" />
                {currentEditId ? 'Update Document' : 'Save Document'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default DocumentForm;
