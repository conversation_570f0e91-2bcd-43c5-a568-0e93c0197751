import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { useDocuments } from '@/contexts/DocumentContext';
import { DOCUMENT_TYPES, DOCUMENT_STATUSES } from '@/models/document';
import { Search, Filter, X } from 'lucide-react';

interface SearchFiltersProps {
}

const SearchFilters: React.FC<SearchFiltersProps> = () => {
  const { searchFilters, setSearchFilters, documents } = useDocuments();

  const handleSearchChange = (value: string) => {
    setSearchFilters({ searchTerm: value });
  };

  const handleTypeFilterChange = (value: string) => {
    setSearchFilters({ typeFilter: value === 'all' ? '' : value as any });
  };

  const handleStatusFilterChange = (value: string) => {
    setSearchFilters({ statusFilter: value === 'all' ? '' : value as any });
  };

  const handleClearFilters = () => {
    setSearchFilters({
      searchTerm: '',
      typeFilter: '',
      statusFilter: '',
    });
  };

  const hasActiveFilters = searchFilters.searchTerm || searchFilters.typeFilter || searchFilters.statusFilter;

  // Count documents by type and status for filter labels
  const typeCounts = DOCUMENT_TYPES.reduce((acc: Record<string, number>, type) => {
    acc[type] = documents.filter(doc => !searchFilters.typeFilter || DOCUMENT_TYPES.includes(doc.status as any)).length;
    return acc;
  }, {} as Record<string, number>);

  const statusCounts = DOCUMENT_STATUSES.reduce((acc: Record<string, number>, status) => {
    acc[status] = documents.filter(doc => !searchFilters.statusFilter || DOCUMENT_STATUSES.includes(doc.status as any)).length;
    return acc;
  }, {} as Record<string, number>);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Search & Filters
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by document title or reference number..."
              value={searchFilters.searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filters Row */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Type Filter */}
            <div>
              <label className="text-sm font-medium mb-2 block">Document Type</label>
              <Select
                value={searchFilters.typeFilter || 'all'}
                onValueChange={handleTypeFilterChange}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types ({documents.length})</SelectItem>
                  {DOCUMENT_TYPES.map(type => (
                    <SelectItem key={type} value={type}>
                      {type} ({typeCounts[type] || 0})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Status Filter */}
            <div>
              <label className="text-sm font-medium mb-2 block">Status</label>
              <Select
                value={searchFilters.statusFilter || 'all'}
                onValueChange={handleStatusFilterChange}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses ({documents.length})</SelectItem>
                  {DOCUMENT_STATUSES.map(status => (
                    <SelectItem key={status} value={status}>
                      {status} ({statusCounts[status] || 0})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Clear Filters */}
            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={handleClearFilters}
                disabled={!hasActiveFilters}
                className="w-full"
              >
                <X className="h-4 w-4 mr-2" />
                Clear Filters
              </Button>
            </div>
          </div>

          {/* Active Filters Summary */}
          {hasActiveFilters && (
            <div className="flex flex-wrap gap-2 pt-2 border-t">
              <span className="text-sm text-muted-foreground">Active filters:</span>
              {searchFilters.searchTerm && (
                <span className="inline-flex items-center px-2 py-1 rounded-md bg-primary/10 text-primary text-xs">
                  Search: "{searchFilters.searchTerm}"
                </span>
              )}
              {searchFilters.typeFilter && (
                <span className="inline-flex items-center px-2 py-1 rounded-md bg-primary/10 text-primary text-xs">
                  Type: {searchFilters.typeFilter}
                </span>
              )}
              {searchFilters.statusFilter && (
                <span className="inline-flex items-center px-2 py-1 rounded-md bg-primary/10 text-primary text-xs">
                  Status: {searchFilters.statusFilter}
                </span>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SearchFilters;
