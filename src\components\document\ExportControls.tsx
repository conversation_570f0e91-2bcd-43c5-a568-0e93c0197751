import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { useDocuments } from '@/contexts/DocumentContext';
import { 
  Download, 
  FileText, 
  Trash2, 
  RefreshCw, 
  ChevronDown,
  Database
} from 'lucide-react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

const ExportControls: React.FC = () => {
  const { documents, resetToSampleData, clearAllDocuments } = useDocuments();

  const exportToPDF = () => {
    const doc = new jsPDF();
    
    // Add title
    doc.setFontSize(20);
    doc.text('Document Management Report', 20, 20);
    
    // Add generation date
    doc.setFontSize(10);
    doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 30);
    
    // Prepare table data
    const tableData = documents.map(document => [
      document.type,
      document.designation,
      document.referenceId,
      document.format,
      new Date(document.effectiveDate).toLocaleDateString(),
      document.version,
      document.status,
      document.fileName || 'No file'
    ]);

    // Add table
    autoTable(doc, {
      head: [['Type', 'Designation', 'Reference ID', 'Format', 'Effective Date', 'Version', 'Status', 'Attachment']],
      body: tableData,
      startY: 40,
      styles: {
        fontSize: 8,
        cellPadding: 2,
      },
      headStyles: {
        fillColor: [66, 139, 202],
        textColor: 255,
      },
      alternateRowStyles: {
        fillColor: [245, 245, 245],
      },
    });

    // Save the PDF
    doc.save('document-management-report.pdf');
  };

  const exportToCSV = () => {
    const headers = ['Type', 'Designation', 'Reference ID', 'Format', 'Effective Date', 'Version', 'Status', 'Attachment', 'Created At', 'Updated At'];
    
    const csvData = documents.map(document => [
      document.type,
      document.designation,
      document.referenceId,
      document.format,
      document.effectiveDate,
      document.version,
      document.status,
      document.fileName || '',
      new Date(document.createdAt).toLocaleDateString(),
      new Date(document.updatedAt).toLocaleDateString()
    ]);

    const csvContent = [
      headers.join(','),
      ...csvData.map(row => row.map(field => `"${field}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'document-management-export.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToJSON = () => {
    const jsonData = {
      exportDate: new Date().toISOString(),
      totalDocuments: documents.length,
      documents: documents
    };

    const blob = new Blob([JSON.stringify(jsonData, null, 2)], { type: 'application/json' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'document-management-export.json');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Data Management
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-2">
          {/* Export Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" disabled={documents.length === 0}>
                <Download className="h-4 w-4 mr-2" />
                Export Data
                <ChevronDown className="h-4 w-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={exportToPDF}>
                <FileText className="h-4 w-4 mr-2" />
                Export as PDF
              </DropdownMenuItem>
              <DropdownMenuItem onClick={exportToCSV}>
                <FileText className="h-4 w-4 mr-2" />
                Export as CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={exportToJSON}>
                <FileText className="h-4 w-4 mr-2" />
                Export as JSON
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Reset to Sample Data */}
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Reset to Sample Data
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Reset to Sample Data</AlertDialogTitle>
                <AlertDialogDescription>
                  This will replace all current documents with sample data. 
                  Any existing documents will be lost. Are you sure you want to continue?
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={resetToSampleData}>
                  Reset Data
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          {/* Clear All Documents */}
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" disabled={documents.length === 0}>
                <Trash2 className="h-4 w-4 mr-2" />
                Clear All Documents
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Clear All Documents</AlertDialogTitle>
                <AlertDialogDescription>
                  This will permanently delete all documents. This action cannot be undone. 
                  Are you sure you want to continue?
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction 
                  onClick={clearAllDocuments}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  Clear All Documents
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>

        {/* Statistics */}
        <div className="mt-4 p-4 bg-muted rounded-lg">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-primary">{documents.length}</div>
              <div className="text-sm text-muted-foreground">Total Documents</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">
                {documents.filter(d => d.status === 'RAS').length}
              </div>
              <div className="text-sm text-muted-foreground">RAS Status</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-yellow-600">
                {documents.filter(d => d.status === 'Modifier').length}
              </div>
              <div className="text-sm text-muted-foreground">To Modify</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {documents.filter(d => d.status === 'Créer').length}
              </div>
              <div className="text-sm text-muted-foreground">To Create</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ExportControls;
