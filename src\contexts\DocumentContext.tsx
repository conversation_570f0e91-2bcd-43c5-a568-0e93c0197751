import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { toast } from '@/hooks/use-toast';

import { AccessLevel, Document, DocumentState, SearchFilters, SortConfig } from '@/models/document';

export interface DocumentContextType extends DocumentState {
  addDocument: (document: Omit<Document, 'id' | 'createdAt' | 'updatedAt' | 'fileName' | 'fileData'>) => void;
  updateDocument: (id: string, document: Omit<Document, 'id' | 'createdAt' | 'updatedAt' | 'fileName' | 'fileData'>) => void;
  deleteDocument: (id: string) => void;
  setCurrentEditId: (id: string | null) => void;
  setSearchFilters: (filters: Partial<SearchFilters>) => void;
  setSortConfig: (config: SortConfig) => void;
  setLoading: (loading: boolean) => void;
  resetToSampleData: () => void;
  clearAllDocuments: () => void;
  getDocumentsByAccessLevel: (accessLevel: AccessLevel) => Document[];
}

type DocumentAction =
  | { type: 'SET_DOCUMENTS'; payload: Document[] }
  | { type: 'ADD_DOCUMENT'; payload: Document }
  | { type: 'UPDATE_DOCUMENT'; payload: { id: string; document: Document } }
  | { type: 'DELETE_DOCUMENT'; payload: string }
  | { type: 'SET_CURRENT_EDIT_ID'; payload: string | null }
  | { type: 'SET_SEARCH_FILTERS'; payload: Partial<SearchFilters> }
  | { type: 'SET_SORT_CONFIG'; payload: SortConfig }
  | { type: 'SET_LOADING'; payload: boolean };


const initialState: DocumentState = {
  documents: [],
  currentEditId: null,
  searchFilters: {
    searchTerm: '',
    typeFilter: '',
    statusFilter: '',
  },
  sortConfig: {
    column: null,
    direction: 'asc',
  },
  isLoading: false,
};

const documentReducer = (state: DocumentState, action: DocumentAction): DocumentState => {
  switch (action.type) {
    case 'SET_DOCUMENTS':
      return { ...state, documents: action.payload };
    case 'ADD_DOCUMENT':
      return { ...state, documents: [...state.documents, action.payload] };
    case 'UPDATE_DOCUMENT':
      return {
        ...state,
        documents: state.documents.map(doc =>
          doc.id === action.payload.id ? action.payload.document : doc
        ),
      };
    case 'DELETE_DOCUMENT':
      return {
        ...state,
        documents: state.documents.filter(doc => doc.id !== action.payload),
      };
    case 'SET_CURRENT_EDIT_ID':
      return { ...state, currentEditId: action.payload };
    case 'SET_SEARCH_FILTERS':
      return {
        ...state,
        searchFilters: { ...state.searchFilters, ...action.payload },
      };
    case 'SET_SORT_CONFIG':
      return { ...state, sortConfig: action.payload };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    default:
      return state;
  }
};

const DocumentContext = createContext<DocumentContextType | undefined>(undefined);

// Local storage utilities
const STORAGE_KEY = 'haccp_documents';

const saveToLocalStorage = (documents: Document[]) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(documents));
  } catch (error) {
    console.error('Failed to save documents to localStorage:', error);
  }
};

const loadFromLocalStorage = (): Document[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Failed to load documents from localStorage:', error);
    return [];
  }
};

// Sample data generator
const generateSampleData = (): Document[] => {
  const now = new Date().toISOString();
  return [
    {
      id: '1',
      documentReferenceNumber: 'PR.ND.01',
      documentTitle: 'Procédure de nettoyage et désinfection',
      version: 2,
      issueDate: '2024-01-15',
      authorFunction: 'Responsable qualité',
      responsibleDepartment: 'Production',
      status: 'RAS',
      storageLocation: 'Serveur central',
      accessLevel: 'Public',
      createdAt: now,
      updatedAt: now,
    },
    {
      id: '2',
      documentReferenceNumber: 'MN.HC.01',
      documentTitle: 'Manuel HACCP',
      version: 1,
      issueDate: '2024-02-01',
      authorFunction: 'Directeur',
      responsibleDepartment: 'Direction',
      status: 'Modifier',
      storageLocation: 'Bureau du directeur',
      accessLevel: 'Internal',
      createdAt: now,
      updatedAt: now,
    },
    {
      id: '3',
      documentReferenceNumber: 'EN.CT.01',
      documentTitle: 'Fiche de contrôle température',
      version: 3,
      issueDate: '2024-01-01',
      authorFunction: 'Agent de contrôle',
      responsibleDepartment: 'Qualité',
      status: 'RAS',
      storageLocation: 'Archive qualité',
      accessLevel: 'Confidential',
      createdAt: now,
      updatedAt: now,
    },
  ];
};

export const DocumentProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(documentReducer, initialState);

  useEffect(() => {
    // Load documents from localStorage on mount
    const savedDocuments = loadFromLocalStorage();
    if (savedDocuments.length === 0) {
      // Generate sample data if no documents exist
      const sampleData = generateSampleData();
      dispatch({ type: 'SET_DOCUMENTS', payload: sampleData });
      saveToLocalStorage(sampleData);
    } else {
      dispatch({ type: 'SET_DOCUMENTS', payload: savedDocuments });
    }
  }, []);

  const addDocument = (documentData: Omit<Document, 'id' | 'createdAt' | 'updatedAt' | 'fileName' | 'fileData'>) => {
    const newDocument: Document = {
      ...documentData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    dispatch({ type: 'ADD_DOCUMENT', payload: newDocument });
    const updatedDocuments = [...state.documents, newDocument];
    saveToLocalStorage(updatedDocuments);

    toast({
      title: "Document ajouté",
      description: "Le document a été ajouté avec succès",
    });
  };

  const updateDocument = (id: string, documentData: Omit<Document, 'id' | 'createdAt' | 'updatedAt' | 'fileName' | 'fileData'>) => {
    const existingDoc = state.documents.find(doc => doc.id === id);
    if (!existingDoc) return;

    const updatedDocument: Document = {
      ...documentData,
      id,
      createdAt: existingDoc.createdAt,
      updatedAt: new Date().toISOString(),
    };
    dispatch({ type: 'UPDATE_DOCUMENT', payload: { id, document: updatedDocument } });
    const updatedDocuments = state.documents.map(doc =>
      doc.id === id ? updatedDocument : doc
    );
    saveToLocalStorage(updatedDocuments);

    toast({
      title: "Document mis à jour",
      description: "Le document a été mis à jour avec succès",
    });
  };

  const deleteDocument = (id: string) => {
    dispatch({ type: 'DELETE_DOCUMENT', payload: id });
    const updatedDocuments = state.documents.filter(doc => doc.id !== id);
    saveToLocalStorage(updatedDocuments);

    toast({
      title: "Document supprimé",
      description: "Le document a été supprimé avec succès",
    });
  };

  const setCurrentEditId = (id: string | null) => {
    dispatch({ type: 'SET_CURRENT_EDIT_ID', payload: id });
  };

  const setSearchFilters = (filters: Partial<SearchFilters>) => {
    dispatch({ type: 'SET_SEARCH_FILTERS', payload: filters });
  };

  const setSortConfig = (config: SortConfig) => {
    dispatch({ type: 'SET_SORT_CONFIG', payload: config });
  };

  const setLoading = (loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  };

  const resetToSampleData = () => {
    const sampleData = generateSampleData();
    dispatch({ type: 'SET_DOCUMENTS', payload: sampleData });
    saveToLocalStorage(sampleData);

    toast({
      title: "Données réinitialisées",
      description: "Les données d'exemple ont été restaurées",
    });
  };

  const clearAllDocuments = () => {
    dispatch({ type: 'SET_DOCUMENTS', payload: [] });
    saveToLocalStorage([]);

    toast({
      title: "Documents supprimés",
      description: "Tous les documents ont été supprimés",
    });
  };

  const getDocumentsByAccessLevel = (accessLevel: AccessLevel): Document[] => {
    return state.documents.filter(doc => doc.accessLevel === accessLevel);
  };

  const contextValue: DocumentContextType = {
    ...state,
    addDocument,
    updateDocument,
    deleteDocument,
    setCurrentEditId,
    setSearchFilters,
    setSortConfig,
    setLoading,
    resetToSampleData,
    clearAllDocuments,
    getDocumentsByAccessLevel,
  };

  return (
    <DocumentContext.Provider value={contextValue}>
      {children}
    </DocumentContext.Provider>
  );
};

export const useDocuments = (): DocumentContextType => {
  const context = useContext(DocumentContext);
  if (!context) {
    throw new Error('useDocuments must be used within a DocumentProvider');
  }
  return context;
};
